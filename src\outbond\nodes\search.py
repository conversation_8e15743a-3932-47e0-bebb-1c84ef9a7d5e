"""Search node for executing search operations."""

import asyncio
import os
import re
from typing import Optional, List, Dict, Any

from langchain_core.messages import SystemMessage, HumanMessage


from outbond.shared.llm_factory import ChatOpenRouterAI, create_llm
from outbond.state import SearchState, SearchPhase, ErrorType, SEARCH_CONFIG, Source
from outbond.configuration import Configuration

from outbond.langchain_crawler import LangChainCrawler
from outbond.prompts import get_current_date_context
from langsmith import Client

def score_content(content: str, query: str) -> float:
    """Score content quality based on relevance to the query.
    
    Args:
        content: The content to score
        query: The search query to match against
        
    Returns:
        Quality score between 0 and 1
    """
    if not content or not query:
        return 0.0
    
    content_lower = content.lower()
    query_lower = query.lower()
    
    # Split query into terms
    query_terms = re.findall(r'\b\w+\b', query_lower)
    if not query_terms:
        return 0.0
    
    # Calculate term frequency score
    term_matches = 0
    for term in query_terms:
        if term in content_lower:
            term_matches += 1
    
    term_score = term_matches / len(query_terms)
    
    # Length bonus (longer content generally better)
    length_score = min(len(content) / 1000, 1.0)  # Max bonus at 1000 chars
    
    # Combine scores
    final_score = (term_score * 0.7) + (length_score * 0.3)
    
    return min(final_score, 1.0)


async def summarize_content(content: str, query: str, llm: Optional[ChatOpenRouterAI] = None) -> str:
    """Summarize content for the given search query.
    
    Args:
        content: The content to summarize
        query: The search query context
        llm: Optional LLM instance
        
    Returns:
        Summary of the content relevant to the query
    """
    try:
        LANGSMITH_API_KEY = os.getenv['LANGSMITH_API_KEY']
        client = Client(api_key=LANGSMITH_API_KEY)
        prompt = client.pull_prompt("summarize_content", include_model=False)

        if llm is None:
            llm = create_llm()
        message = {"date_context":{get_current_date_context()}, "summary_chart_limit":SEARCH_CONFIG.SUMMARY_CHAR_LIMIT ,"content": f'Query: "{query}"\n\nContent: {content[:2000]}'}
        chain = prompt | llm
        response = await chain.ainvoke(message)
        
    
        return str(response.content).strip()
    except Exception:
        return ''


async def search_node(state: SearchState, config: Optional[Configuration] = None) -> SearchState:
    """Search node that executes search operations using Firecrawl.
    
    This implementation:
    1. Processes search queries sequentially
    2. Uses Firecrawl to search and get content
    3. Scores and summarizes content for relevance
    4. Supports both parallel and sequential processing
    
    Args:
        state: The current SearchState
        config: Optional configuration
        
    Returns:
        Updated SearchState with search results processed
    """
    # Initialize LangChain crawler
    #   firecrawl = FirecrawlClient()
    firecrawl = LangChainCrawler()
    
    search_queries = state.searchQueries or []
    current_index = state.currentSearchIndex or 0
    
    # Check if we're done with all searches
    if current_index >= len(search_queries):
        return SearchState(
            query=state.query,
            context=state.context,
            understanding=state.understanding,
            searchQueries=state.searchQueries,
            currentSearchIndex=state.currentSearchIndex,
            sources=state.sources,
            scrapedSources=state.scrapedSources,
            processedSources=state.processedSources,
            finalAnswer=state.finalAnswer,
            followUpQuestions=state.followUpQuestions,
            subQueries=state.subQueries,
            searchAttempt=state.searchAttempt,
            retryCount=state.retryCount,
            phase=SearchPhase.SCRAPING,  # Move to next phase when done
            error=state.error,
            errorType=state.errorType,
            maxRetries=state.maxRetries
        )
    
    search_query = search_queries[current_index]
    
    try:
        # Perform search with Firecrawl
        results = await firecrawl.search(search_query, {
            'limit': SEARCH_CONFIG.MAX_SOURCES_PER_SEARCH,
            'scrapeOptions': {
                'formats': ['markdown']
            }
        })
        
        # Transform results to Source objects
        new_sources: List[Source] = []
        for r in results['data']:
            source = Source(
                url=r['url'],
                title=r['title'],
                content=r.get('markdown') or r.get('content', ''),
                quality=0  # Will be set below
            )
            new_sources.append(source)
        
        # Process sources for scoring and summarization
        if SEARCH_CONFIG.PARALLEL_SUMMARY_GENERATION:
            # Parallel processing for better performance
            async def process_source(source: Source) -> None:
                # Score the content
                source.quality = score_content(source.content or '', state.query)
                
                # Generate summary if content is available
                if (source.content and  len(source.content) > SEARCH_CONFIG.MIN_CONTENT_LENGTH):
                    summary = await summarize_content(source.content, search_query)
                    # Store the summary if it's meaningful
                    if summary and not summary.lower().startswith('no specific'):
                        source.summary = summary
                else: # TODO Check if helps, otherwise remove
                    if source.content:
                        summary = source.content
                    else:
                        summary = ''
            # Process all sources in parallel
            await asyncio.gather(*[process_source(source) for source in new_sources])
            
        else:
            # Sequential processing
            llm = create_llm()
            
            for source in new_sources:
               
                
                # Score the content
                source.quality = score_content(source.content or '', state.query)
                
                # Generate summary if content is available
                if (source.content and 
                    len(source.content) > SEARCH_CONFIG.MIN_CONTENT_LENGTH):
                    
                    summary = await summarize_content(source.content, search_query, llm)
                    
                    # Store the summary if it's meaningful
                    if summary and not summary.lower().startswith('no specific'):
                        source.summary = summary
        
        # Return updated state with new sources and incremented index
        return SearchState(
            query=state.query,
            context=state.context,
            understanding=state.understanding,
            searchQueries=state.searchQueries,
            currentSearchIndex=current_index + 1,
            sources=new_sources,  # This will be merged with existing sources by the reducer
            scrapedSources=state.scrapedSources,
            processedSources=state.processedSources,
            finalAnswer=state.finalAnswer,
            followUpQuestions=state.followUpQuestions,
            subQueries=state.subQueries,
            searchAttempt=state.searchAttempt,
            retryCount=state.retryCount,
            phase=state.phase,  # Keep current phase until all searches complete
            error=state.error,
            errorType=state.errorType,
            maxRetries=state.maxRetries
        )
        
    except Exception as search_error:
        # Handle individual search errors by moving to next search
        return SearchState(
            query=state.query,
            context=state.context,
            understanding=state.understanding,
            searchQueries=state.searchQueries,
            currentSearchIndex=current_index + 1,
            sources=state.sources,
            scrapedSources=state.scrapedSources,
            processedSources=state.processedSources,
            finalAnswer=state.finalAnswer,
            followUpQuestions=state.followUpQuestions,
            subQueries=state.subQueries,
            searchAttempt=state.searchAttempt,
            retryCount=state.retryCount,
            phase=state.phase,
            error=f"Search error for query '{search_query}': {str(search_error)}",
            errorType=ErrorType.SEARCH_ERROR,
            maxRetries=state.maxRetries
        )
        
  