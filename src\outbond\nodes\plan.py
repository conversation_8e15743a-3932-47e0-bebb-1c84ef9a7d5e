import json
import os
from langchain_core.messages import SystemMessage, HumanMessage
from typing import Dict, List, Optional
from outbond.configuration import Configuration
from outbond.shared.llm_factory import ChatOpenRouterAI, create_llm
from outbond.state import SEARCH_CONFIG, ErrorType, SearchPhase, SearchState, SubQuery
from langsmith import Client


async def extract_sub_queries(query: str, llm: Optional[ChatOpenRouterAI] = None) -> List[Dict[str, str]]:
    """Extract sub-queries from a complex query.
    
    Args:
        query: The main query to break down
        llm: Optional LLM instance
        
    Returns:
        List of dictionaries with 'question' and 'searchQuery' keys
    """
 

    try:      
        LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
        client = Client(api_key=LANGSMITH_API_KEY)
        prompt = client.pull_prompt("extract_sub_queries", include_model=False)
    
        if llm is None:
             llm = create_llm()
        message = {"query":query }
        chain = prompt | llm
        response = await chain.ainvoke(message)
        
       
        return json.loads(str(response.content))
    except Exception:
        # Fallback: treat as single query
        return [{"question": query, "searchQuery": query}]


async def generate_alternative_search_queries(
    sub_queries: List[SubQuery], 
    search_attempt: int,
    llm: Optional[ChatOpenRouterAI] = None
) -> List[str]:
    """Generate alternative search queries for retry attempts.
    
    Args:
        sub_queries: List of sub-queries to generate alternatives for
        search_attempt: Current search attempt number
        llm: Optional LLM instance
        
    Returns:
        List of alternative search queries
    """
    unanswered_queries = [sq for sq in sub_queries 
                         if not sq['answered'] or sq['confidence'] < SEARCH_CONFIG.MIN_ANSWER_CONFIDENCE]
    
    # if not unanswered_queries:
    #     return []
    try:
        LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
        client = Client(api_key=LANGSMITH_API_KEY)
        prompt = client.pull_prompt("generate_alternative_search_queries", include_model=False)

        if llm is None:
             llm = create_llm()
        
        query_text = "\n".join([f"- {sq['question']}" for sq in unanswered_queries])
        message = {"search_attempt":search_attempt, "query_text":query_text }
        chain = prompt | llm
        response = await chain.ainvoke(message)

        return json.loads(str(response.content))
    except Exception:
        # Fallback: add "alternative" prefix to original queries
        return [f"alternative {sq['searchQuery']}" for sq in unanswered_queries]
    
async def plan_node(state: SearchState, config: Optional[Configuration] = None) -> SearchState:
    """Plan node that handles the planning phase with sub-query extraction.
    
    Args:
        state: The current SearchState
        config: Optional configuration
        
    Returns:
        Updated SearchState with planning phase completed
    """
    try:
        # Extract sub-queries if not already done
        sub_queries = state.subQueries
        if not sub_queries:
            extracted = await extract_sub_queries(state.query)
            sub_queries = []
            for sq in extracted:
                sub_queries.append({
                    "question": sq["question"],
                    "searchQuery": sq["searchQuery"],
                    "answered": False,
                    "answer": None,
                    "confidence": 0.0,
                    "sources": []
                })
        
        # Generate search queries for unanswered questions
        unanswered_queries = [sq for sq in sub_queries 
                             if not sq["answered"] or sq["confidence"] < SEARCH_CONFIG.MIN_ANSWER_CONFIDENCE]
        
        if len(unanswered_queries) == 0:
            # All questions answered, skip to analysis
            return SearchState(
       
                subQueries=sub_queries,
                phase=SearchPhase.ANALYZING,
 
            )
       
        # Use alternative search queries if this is a retry
        search_queries: List[str]
        if state.searchAttempt > 0:
            search_queries = await generate_alternative_search_queries(sub_queries, state.searchAttempt)
            
            # Update sub-queries with new search queries
            alternative_index = 0
            for sq in sub_queries:
                if not sq["answered"] or sq["confidence"] < SEARCH_CONFIG.MIN_ANSWER_CONFIDENCE:
                    if alternative_index < len(search_queries):
                        sq["searchQuery"] = search_queries[alternative_index]
                        alternative_index += 1
        else:
            # First attempt - use the search queries from sub-queries
            search_queries = [sq["searchQuery"] for sq in unanswered_queries]
        
        return SearchState(
            searchQueries=search_queries,
            currentSearchIndex=0  ,
            subQueries=sub_queries,
            phase=SearchPhase.SEARCHING,
        )
        
    except Exception as error:
        return SearchState(
 
            phase=SearchPhase.ERROR,
            error=str(error) if isinstance(error, Exception) else 'Failed to plan search',
            errorType=ErrorType.LLM_ERROR,
    
        ) 