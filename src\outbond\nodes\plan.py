import json
import logging
import os
from langchain_core.messages import SystemMessage, HumanMessage
from typing import Dict, List, Optional
from outbond.configuration import Configuration
from outbond.shared.llm_factory import ChatOpenRouterAI, create_llm
from outbond.state import SEARCH_CONFIG, ErrorType, SearchPhase, SearchState, SubQuery
from langsmith import Client

logger = logging.getLogger(__name__)


async def extract_sub_queries(query: str, llm: Optional[ChatOpenRouterAI] = None) -> List[Dict[str, str]]:
    """Extract sub-queries from a complex query.

    Args:
        query: The main query to break down
        llm: Optional LLM instance

    Returns:
        List of dictionaries with 'question' and 'searchQuery' keys
    """
    logger.info(f"Extracting sub-queries from query: '{query}'")

    try:
        LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
        logger.debug("Connecting to LangSmith client for extract_sub_queries prompt")
        client = Client(api_key=LANGSMITH_API_KEY)
        prompt = client.pull_prompt("extract_sub_queries", include_model=False)
        logger.debug("Successfully pulled extract_sub_queries prompt from LangSmith")

        if llm is None:
            logger.debug("Creating new LLM instance for sub-query extraction")
            llm = create_llm()
        else:
            logger.debug("Using provided LLM instance")

        message = {"query": query}
        chain = prompt | llm
        logger.debug("Invoking LLM chain for sub-query extraction")
        response = await chain.ainvoke(message)

        result = json.loads(str(response.content))
        logger.info(f"Successfully extracted {len(result)} sub-queries")
        for i, sq in enumerate(result):
            logger.debug(f"Sub-query {i+1}: question='{sq.get('question', 'N/A')}', searchQuery='{sq.get('searchQuery', 'N/A')}'")

        return result
    except Exception as e:
        logger.warning(f"Failed to extract sub-queries via LLM: {str(e)}, falling back to single query")
        # Fallback: treat as single query
        fallback_result = [{"question": query, "searchQuery": query}]
        logger.debug(f"Fallback result: {fallback_result}")
        return fallback_result


async def generate_alternative_search_queries(
    sub_queries: List[SubQuery],
    search_attempt: int,
    llm: Optional[ChatOpenRouterAI] = None
) -> List[str]:
    """Generate alternative search queries for retry attempts.

    Args:
        sub_queries: List of sub-queries to generate alternatives for
        search_attempt: Current search attempt number
        llm: Optional LLM instance

    Returns:
        List of alternative search queries
    """
    logger.info(f"Generating alternative search queries for attempt {search_attempt}")

    unanswered_queries = [sq for sq in sub_queries
                         if not sq['answered'] or sq['confidence'] < SEARCH_CONFIG.MIN_ANSWER_CONFIDENCE]

    logger.debug(f"Found {len(unanswered_queries)} unanswered queries to generate alternatives for")
    for i, sq in enumerate(unanswered_queries):
        logger.debug(f"Unanswered query {i+1}: '{sq['question']}' (confidence: {sq['confidence']})")

    try:
        LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
        logger.debug("Connecting to LangSmith client for generate_alternative_search_queries prompt")
        client = Client(api_key=LANGSMITH_API_KEY)
        prompt = client.pull_prompt("generate_alternative_search_queries", include_model=False)
        logger.debug("Successfully pulled generate_alternative_search_queries prompt from LangSmith")

        if llm is None:
            logger.debug("Creating new LLM instance for alternative query generation")
            llm = create_llm()
        else:
            logger.debug("Using provided LLM instance")

        query_text = "\n".join([f"- {sq['question']}" for sq in unanswered_queries])
        logger.debug(f"Query text for LLM:\n{query_text}")

        message = {"search_attempt": search_attempt, "query_text": query_text}
        chain = prompt | llm
        logger.debug("Invoking LLM chain for alternative query generation")
        response = await chain.ainvoke(message)

        result = json.loads(str(response.content))
        logger.info(f"Successfully generated {len(result)} alternative search queries")
        for i, query in enumerate(result):
            logger.debug(f"Alternative query {i+1}: '{query}'")

        return result
    except Exception as e:
        logger.warning(f"Failed to generate alternative queries via LLM: {str(e)}, falling back to prefixed queries")
        # Fallback: add "alternative" prefix to original queries
        fallback_result = [f"alternative {sq['searchQuery']}" for sq in unanswered_queries]
        logger.debug(f"Fallback alternative queries: {fallback_result}")
        return fallback_result
    
async def plan_node(state: SearchState, config: Optional[Configuration] = None) -> SearchState:
    """Plan node that handles the planning phase with sub-query extraction.

    Args:
        state: The current SearchState
        config: Optional configuration

    Returns:
        Updated SearchState with planning phase completed
    """
    logger.info("Starting plan_node execution")
    logger.debug(f"Input state - query: '{state.query}', phase: {state.phase}, searchAttempt: {state.searchAttempt}")
    logger.debug(f"Existing subQueries count: {len(state.subQueries) if state.subQueries else 0}")

    try:
        # Extract sub-queries if not already done
        sub_queries = state.subQueries

        if not sub_queries:
            logger.info("No existing sub-queries found, extracting from main query")
            extracted = await extract_sub_queries(state.query)
            logger.debug(f"Extracted {len(extracted)} sub-queries from main query")

            sub_queries = []
            for i, sq in enumerate(extracted):
                logger.debug(f"Processing sub-query {i+1}: question='{sq['question']}', searchQuery='{sq['searchQuery']}'")
                sub_queries.append({
                    "question": sq["question"],
                    "searchQuery": sq["searchQuery"],
                    "answered": False,
                    "answer": None,
                    "confidence": 0.0,
                    "sources": []
                })
            logger.info(f"Created {len(sub_queries)} sub-query objects")
        else:
            logger.info(f"Using existing {len(sub_queries)} sub-queries")

        # Generate search queries for unanswered questions
        unanswered_queries = [sq for sq in sub_queries
                             if not sq["answered"] or sq["confidence"] < SEARCH_CONFIG.MIN_ANSWER_CONFIDENCE]

        logger.debug(f"Found {len(unanswered_queries)} unanswered queries (confidence threshold: {SEARCH_CONFIG.MIN_ANSWER_CONFIDENCE})")

        if len(unanswered_queries) == 0:
            # All questions answered, skip to analysis
            logger.info("All sub-queries answered with sufficient confidence, transitioning to ANALYZING phase")
            return SearchState(
                subQueries=sub_queries,
                phase=SearchPhase.ANALYZING,
            )

        # Use alternative search queries if this is a retry
        search_queries: List[str]
        if state.searchAttempt > 0:
            logger.info(f"Retry attempt {state.searchAttempt}, generating alternative search queries")
            search_queries = await generate_alternative_search_queries(sub_queries, state.searchAttempt)
            logger.debug(f"Generated {len(search_queries)} alternative search queries")

            # Update sub-queries with new search queries
            alternative_index = 0
            for i, sq in enumerate(sub_queries):
                if not sq["answered"] or sq["confidence"] < SEARCH_CONFIG.MIN_ANSWER_CONFIDENCE:
                    if alternative_index < len(search_queries):
                        old_query = sq["searchQuery"]
                        sq["searchQuery"] = search_queries[alternative_index]
                        logger.debug(f"Updated sub-query {i+1} search query: '{old_query}' -> '{sq['searchQuery']}'")
                        alternative_index += 1
            logger.info(f"Updated {alternative_index} sub-queries with alternative search queries")
        else:
            # First attempt - use the search queries from sub-queries
            search_queries = [sq["searchQuery"] for sq in unanswered_queries]
            logger.info(f"First search attempt, using {len(search_queries)} original search queries")
            for i, query in enumerate(search_queries):
                logger.debug(f"Search query {i+1}: '{query}'")

        logger.info(f"Transitioning to SEARCHING phase with {len(search_queries)} search queries")
        return SearchState(
            searchQueries=search_queries,
            currentSearchIndex=0,
            subQueries=sub_queries,
            phase=SearchPhase.SEARCHING,
        )

    except Exception as error:
        logger.error(f"Error in plan_node: {str(error)}", exc_info=True)
        return SearchState(
            phase=SearchPhase.ERROR,
            error=str(error) if isinstance(error, Exception) else 'Failed to plan search',
            errorType=ErrorType.LLM_ERROR,
        )